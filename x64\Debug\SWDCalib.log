﻿生成启动时间为 2023/11/16 9:26:37。
     1>项目“D:\SWDC\JXDC\JXDC-VC10-WRH\SWDCalib\SWDCalib.vcxproj”在节点 2 上(build 个目标)。
     1>InitializeBuildStatus:
         正在创建“x64\Debug\SWDCalib.unsuccessfulbuild”，因为已指定“AlwaysCreate”。
       ClCompile:
         d:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\x86_amd64\CL.exe /c /ID:\openCV\opencv\build\include /Zi /nologo /W3 /WX- /Od /D WIN32 /D _WINDOWS /D _DEBUG /D _MBCS /Gm /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Yc"StdAfx.h" /Fp"x64\Debug\SWDCalib.pch" /Fo"x64\Debug\\" /Fd"x64\Debug\vc100.pdb" /Gd /TP /errorReport:prompt stdafx.cpp
         stdafx.cpp
         d:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\x86_amd64\CL.exe /c /ID:\openCV\opencv\build\include /Zi /nologo /W3 /WX- /Od /D WIN32 /D _WINDOWS /D _DEBUG /D _MBCS /Gm /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp"x64\Debug\SWDCalib.pch" /Fo"x64\Debug\\" /Fd"x64\Debug\vc100.pdb" /Gd /TP /errorReport:prompt ..\JXDCCmn.cpp ..\WZDDRDLG.CPP DSPDlg.cpp GetCameraNumber.cpp JXDCCalibrateDlg.cpp Resect.cpp SWDCalib.cpp SaveResult.cpp tinyxml2.cpp
         Skipping... (no relevant changes detected)
         tinyxml2.cpp
         SaveResult.cpp
         SWDCalib.cpp
         Resect.cpp
         JXDCCalibrateDlg.cpp
         GetCameraNumber.cpp
         DSPDlg.cpp
         WZDDRDLG.CPP
         JXDCCmn.cpp
       ResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /D _DEBUG /l"0x0804" /Ix64\Debug\ /nologo /fo"x64\Debug\SWDCalib.res" SWDCalib.rc 
       ManifestResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /nologo /fo"x64\Debug\SWDCalib.exe.embed.manifest.res" x64\Debug\SWDCalib_manifest.rc 
       Link:
         d:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\x86_amd64\link.exe /ERRORREPORT:PROMPT /OUT:"D:\SWDC\JXDC\JXDC-VC10-WRH\SWDCalib\x64\Debug\SWDCalib.exe" /INCREMENTAL /NOLOGO /LIBPATH:D:\openCV\opencv\build\x64\vc14\lib opencv_calib3d2413d.lib opencv_features2d2413d.lib opencv_flann2413d.lib opencv_core2413d.lib opencv_imgproc2413d.lib opencv_highgui2413d.lib /NODEFAULTLIB:LIBCMT.lib /MANIFEST /ManifestFile:"x64\Debug\SWDCalib.exe.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:"D:\SWDC\JXDC\JXDC-VC10-WRH\SWDCalib\x64\Debug\SWDCalib.pdb" /SUBSYSTEM:WINDOWS /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\SWDC\JXDC\JXDC-VC10-WRH\SWDCalib\x64\Debug\SWDCalib.lib" /MACHINE:X64 x64\Debug\SWDCalib.res
         x64\Debug\SWDCalib.exe.embed.manifest.res
         x64\Debug\JXDCCmn.obj
         x64\Debug\WZDDRDLG.obj
         x64\Debug\DSPDlg.obj
         x64\Debug\GetCameraNumber.obj
         x64\Debug\JXDCCalibrateDlg.obj
         x64\Debug\Resect.obj
         x64\Debug\SaveResult.obj
         x64\Debug\stdafx.obj
         x64\Debug\SWDCalib.obj
         x64\Debug\tinyxml2.obj
         SWDCalib.vcxproj -> D:\SWDC\JXDC\JXDC-VC10-WRH\SWDCalib\x64\Debug\SWDCalib.exe
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /out:"x64\Debug\SWDCalib.exe.embed.manifest" /manifest x64\Debug\SWDCalib.exe.intermediate.manifest "d:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
         所有输出均为最新。
       LinkEmbedManifest:
         所有输出均为最新。
         SWDCalib.vcxproj -> D:\SWDC\JXDC\JXDC-VC10-WRH\SWDCalib\x64\Debug\SWDCalib.exe
       FinalizeBuildStatus:
         正在删除文件“x64\Debug\SWDCalib.unsuccessfulbuild”。
         正在对“x64\Debug\SWDCalib.lastbuildstate”执行 Touch 任务。
     1>已完成生成项目“D:\SWDC\JXDC\JXDC-VC10-WRH\SWDCalib\SWDCalib.vcxproj”(build 个目标)的操作。

生成成功。

已用时间 00:00:02.95
