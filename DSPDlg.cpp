// DSPDlg.cpp : implementation file
//

#include "stdafx.h"
#include "SWDCalib.h"
#include "DSPDlg.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CDSPDlg dialog

CDSPDlg::CDSPDlg(CWnd* pParent /*=NULL*/)
	: CDialog(CDSPDlg::IDD, pParent)
{
	//{{AFX_DATA_INIT(CDSPDlg)
	m_sTitle = _T("");
	//}}AFX_DATA_INIT
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
}

void CDSPDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CDSPDlg)
	DDX_Text(pDX, IDC_TITLE, m_sTitle);
	//}}AFX_DATA_MAP
}

void CDSPDlg::CenterWindow()
{
	if (GetSafeHwnd()&&(!IsIconic()))
	{
		int	nScreenWidth,nScreenHeight;
		CRect rectWnd;
		GetWindowRect(&rectWnd);
		nScreenWidth=GetSystemMetrics(SM_CXSCREEN);
		nScreenHeight=GetSystemMetrics(SM_CYSCREEN);
		rectWnd.right-=rectWnd.left;
		rectWnd.bottom-=rectWnd.top;
		rectWnd.left=(nScreenWidth-rectWnd.right)>>1;
		rectWnd.top=(nScreenHeight-rectWnd.bottom)>>1;
		rectWnd.right+=rectWnd.left;
		rectWnd.bottom+=rectWnd.top;
		MoveWindow(&rectWnd);
	}
}

BEGIN_MESSAGE_MAP(CDSPDlg, CDialog)
	//{{AFX_MSG_MAP(CDSPDlg)
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CDSPDlg message handlers

BOOL CDSPDlg::OnInitDialog()
{
	CDialog::OnInitDialog();

	SetIcon(m_hIcon, TRUE);			// Set big icon
	SetIcon(m_hIcon, FALSE);		// Set small icon
	
	// TODO: Add extra initialization here
	
	return TRUE;  // return TRUE  unless you set the focus to a control
}

// If you add a minimize button to your dialog, you will need the code below
//  to draw the icon.  For MFC applications using the document/view model,
//  this is automatically done for you by the framework.

void CDSPDlg::OnPaint() 
{
	if (IsIconic())
	{
		CPaintDC dc(this); // device context for painting

		SendMessage(WM_ICONERASEBKGND, (WPARAM) dc.GetSafeHdc(), 0);

		// Center icon in client rectangle
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// Draw the icon
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialog::OnPaint();
	}
}

HCURSOR CDSPDlg::OnQueryDragIcon()
{
//	return (HCURSOR) m_hIcon;
	return static_cast<HCURSOR>(m_hIcon);
}

BOOL CDSPDlg::Create() 
{
	// TODO: Add your specialized code here and/or call the base class
	
	return CDialog::Create(IDD, NULL);
}
