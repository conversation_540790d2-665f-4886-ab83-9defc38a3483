// DSPDlg.h : header file
//

#if !defined(AFX_DSPDLG_H__55CCE3F4_4FB5_11D3_8D66_006008165E4F__INCLUDED_)
#define AFX_DSPDLG_H__55CCE3F4_4FB5_11D3_8D66_006008165E4F__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000

/////////////////////////////////////////////////////////////////////////////
// CDSPDlg dialog

class CDSPDlg : public CDialog
{
// Construction
public:
	CDSPDlg(CWnd* pParent = NULL);	// standard constructor

	void CenterWindow();

// Dialog Data
	//{{AFX_DATA(CDSPDlg)
	enum { IDD = IDD_DSP_DIALOG };
	CString	m_sTitle;
	//}}AFX_DATA

	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CDSPDlg)
	public:
	virtual BOOL Create();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:
	HICON m_hIcon;

public:
	// Generated message map functions
	//{{AFX_MSG(CDSPDlg)
	virtual BOOL OnInitDialog();
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_DSPDLG_H__55CCE3F4_4FB5_11D3_8D66_006008165E4F__INCLUDED_)
