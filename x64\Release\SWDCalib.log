﻿生成启动时间为 2025/7/25 23:19:02。
     1>项目“F:\JXDC-VC10-WRH\SWDCalib\SWDCalib.vcxproj”在节点 2 上(build 个目标)。
     1>InitializeBuildStatus:
         正在创建“x64\Release\SWDCalib.unsuccessfulbuild”，因为已指定“AlwaysCreate”。
       ClCompile:
         D:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\x86_amd64\CL.exe /c /ID:\SWDC\JXDC\Packages\Opencv231\build\include /Zi /nologo /W3 /WX- /O2 /Oi /GL /D WIN32 /D _WINDOWS /D NDEBUG /D _CRT_SECURE_NO_WARNINGS /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yc"StdAfx.h" /Fp"x64\Release\SWDCalib.pch" /Fo"x64\Release\\" /Fd"x64\Release\vc100.pdb" /Gd /TP /errorReport:prompt stdafx.cpp
         stdafx.cpp
         D:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\x86_amd64\CL.exe /c /ID:\SWDC\JXDC\Packages\Opencv231\build\include /Zi /nologo /W3 /WX- /O2 /Oi /GL /D WIN32 /D _WINDOWS /D NDEBUG /D _CRT_SECURE_NO_WARNINGS /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp"x64\Release\SWDCalib.pch" /Fo"x64\Release\\" /Fd"x64\Release\vc100.pdb" /Gd /TP /errorReport:prompt ..\JXDCCmn.cpp ..\WZDDRDLG.CPP DSPDlg.cpp GetCameraNumber.cpp JXDCCalibrateDlg.cpp Resect.cpp SaveResult.cpp SWDCalib.cpp tinyxml2.cpp
         JXDCCmn.cpp
         WZDDRDLG.CPP
         DSPDlg.cpp
         GetCameraNumber.cpp
         JXDCCalibrateDlg.cpp
         Resect.cpp
     1>Resect.cpp(6): fatal error C1083: 无法打开包括文件:“opencv2\opencv.hpp”: No such file or directory
         SaveResult.cpp
         SWDCalib.cpp
         tinyxml2.cpp
     1>已完成生成项目“F:\JXDC-VC10-WRH\SWDCalib\SWDCalib.vcxproj”(build 个目标)的操作 - 失败。

生成失败。

已用时间 00:00:03.08
